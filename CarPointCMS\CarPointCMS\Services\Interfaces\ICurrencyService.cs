using CarPointCMS.Models.Entities;

namespace CarPointCMS.Services.Interfaces
{
    public interface ICurrencyService
    {
        Task<IEnumerable<Currency>> GetAllCurrenciesAsync();
        Task<Currency?> GetCurrencyByIdAsync(int id);
        Task<Currency?> GetDefaultCurrencyAsync();
        Task<Currency> GetCurrentCurrencyAsync();
        Task<Currency> CreateCurrencyAsync(Currency currency);
        Task<Currency> UpdateCurrencyAsync(Currency currency);
        Task<bool> DeleteCurrencyAsync(int id);
        Task<bool> SetDefaultCurrencyAsync(int id);
        Task<bool> ExistsAsync(int id);

        // Currency Conversion
        Task<decimal> ConvertAmountAsync(decimal amount, int fromCurrencyId, int toCurrencyId);
        Task<decimal> ConvertToDefaultAsync(decimal amount, int fromCurrencyId);
        Task<decimal> ConvertFromDefaultAsync(decimal amount, int toCurrencyId);
        Task<string> FormatAmountAsync(decimal amount, int currencyId);
        Task<string> FormatAmountWithDefaultCurrencyAsync(decimal amount);

        // Exchange Rate Management
        Task<bool> UpdateExchangeRateAsync(int currencyId, decimal newRate);
        Task<bool> UpdateAllExchangeRatesAsync(Dictionary<int, decimal> rates);
        Task<DateTime?> GetLastUpdateTimeAsync();

        // Utility Methods
        string GetCurrencySymbol(Currency currency);
        string FormatCurrency(decimal amount, string symbol);
        Task<List<Currency>> GetActiveCurrenciesAsync();
    }
}
