using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Routing;
using CarPointCMS.Services;
using CarPointCMS.Models.Entities;
using CarPointCMS.Common;
using System.Text.RegularExpressions;

namespace CarPointCMS.Controllers
{
    public abstract class BaseController : Controller
    {
        protected readonly IAuthenticationService _authService;

        protected BaseController(IAuthenticationService authService)
        {
            _authService = authService;
        }

        // Current user properties
        protected bool IsAuthenticated => _authService.IsAuthenticated(HttpContext);
        protected string? CurrentUserRole => _authService.GetCurrentUserRole(HttpContext);
        protected int? CurrentUserId => _authService.GetCurrentUserId(HttpContext);
        protected bool IsAdmin => _authService.IsInRole(HttpContext, UserRoles.Admin);
        protected bool IsCustomer => _authService.IsInRole(HttpContext, UserRoles.Customer);

        // Get current user entities
        protected async Task<Admin?> GetCurrentAdminAsync()
        {
            return await _authService.GetCurrentAdminAsync(HttpContext);
        }

        protected async Task<User?> GetCurrentCustomerAsync()
        {
            return await _authService.GetCurrentCustomerAsync(HttpContext);
        }

        // Authentication methods
        protected async Task<bool> SignInAdminAsync(Admin admin)
        {
            return await _authService.SignInAdminAsync(HttpContext, admin);
        }

        protected async Task<bool> SignInCustomerAsync(User customer)
        {
            return await _authService.SignInCustomerAsync(HttpContext, customer);
        }

        protected async Task SignOutAsync()
        {
            await _authService.SignOutAsync(HttpContext);
        }

        // Helper methods for common operations
        protected void SetSuccessMessage(string message)
        {
            TempData["success"] = message;
        }

        protected void SetErrorMessage(string message)
        {
            TempData["error"] = message;
        }

        protected void SetInfoMessage(string message)
        {
            TempData["info"] = message;
        }

        protected void SetWarningMessage(string message)
        {
            TempData["warning"] = message;
        }

        // Area-aware redirect helpers
        protected IActionResult RedirectToAdminLogin(string? message = null)
        {
            if (!string.IsNullOrEmpty(message))
            {
                SetErrorMessage(message);
            }
            return RedirectToAction("Login", "Auth", new { area = "Admin" });
        }

        protected IActionResult RedirectToCustomerLogin(string? message = null)
        {
            if (!string.IsNullOrEmpty(message))
            {
                SetErrorMessage(message);
            }
            return RedirectToAction("Login", "Auth", new { area = "Customer" });
        }

        /// <summary>
        /// Area-aware RedirectToAction that automatically includes the current area
        /// </summary>
        protected IActionResult RedirectToActionInArea(string actionName)
        {
            var currentArea = GetCurrentArea();
            return RedirectToAction(actionName, null, new { area = currentArea });
        }

        /// <summary>
        /// Area-aware RedirectToAction that automatically includes the current area
        /// </summary>
        protected IActionResult RedirectToActionInArea(string actionName, string controllerName)
        {
            var currentArea = GetCurrentArea();
            return RedirectToAction(actionName, controllerName, new { area = currentArea });
        }

        /// <summary>
        /// Area-aware RedirectToAction that automatically includes the current area with route values
        /// </summary>
        protected IActionResult RedirectToActionInArea(string actionName, object? routeValues)
        {
            var currentArea = GetCurrentArea();
            var routeValueDict = new RouteValueDictionary(routeValues);
            routeValueDict["area"] = currentArea;
            return RedirectToAction(actionName, routeValueDict);
        }

        /// <summary>
        /// Area-aware RedirectToAction that automatically includes the current area with route values
        /// </summary>
        protected IActionResult RedirectToActionInArea(string actionName, string controllerName, object? routeValues)
        {
            var currentArea = GetCurrentArea();
            var routeValueDict = new RouteValueDictionary(routeValues);
            routeValueDict["area"] = currentArea;
            return RedirectToAction(actionName, controllerName, routeValueDict);
        }

        /// <summary>
        /// Gets the current area from the route data
        /// </summary>
        private string? GetCurrentArea()
        {
            return ControllerContext.RouteData.Values["area"]?.ToString();
        }

        protected IActionResult RedirectToUnauthorized()
        {
            return View("Unauthorized");
        }

        // File upload helpers
        protected async Task<string> SaveUploadedFileAsync(IFormFile file, string folder)
        {
            var uploadsFolder = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", "uploads", folder);
            Directory.CreateDirectory(uploadsFolder);

            var fileName = Guid.NewGuid().ToString() + Path.GetExtension(file.FileName);
            var filePath = Path.Combine(uploadsFolder, fileName);

            using (var fileStream = new FileStream(filePath, FileMode.Create))
            {
                await file.CopyToAsync(fileStream);
            }

            return fileName;
        }

        protected void DeleteFile(string fileName, string folder)
        {
            if (string.IsNullOrEmpty(fileName)) return;

            var filePath = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", "uploads", folder, fileName);
            if (System.IO.File.Exists(filePath))
            {
                System.IO.File.Delete(filePath);
            }
        }

        // Validation helpers
        protected bool IsValidEmail(string email)
        {
            try
            {
                var addr = new System.Net.Mail.MailAddress(email);
                return addr.Address == email;
            }
            catch
            {
                return false;
            }
        }

        protected string GenerateSlug(string input)
        {
            if (string.IsNullOrEmpty(input))
                return string.Empty;

            string slug = input.ToLower();
            slug = Regex.Replace(slug, @"[^a-z0-9\s-]", "");
            slug = Regex.Replace(slug, @"\s+", "-");
            slug = Regex.Replace(slug, @"-+", "-");
            slug = slug.Trim('-');

            return slug;
        }

        // Legacy method names for backward compatibility
        protected bool IsAdminLoggedIn() => IsAdmin && IsAuthenticated;
        protected bool IsCustomerLoggedIn() => IsCustomer && IsAuthenticated;

        protected IActionResult RedirectToLogin(string? message = null)
        {
            if (IsAdmin || CurrentUserRole == UserRoles.Admin)
                return RedirectToAdminLogin(message);
            else
                return RedirectToCustomerLogin(message);
        }

        /// <summary>
        /// Redirects to the appropriate login based on the current area
        /// </summary>
        protected IActionResult RedirectToAreaLogin(string? message = null)
        {
            var currentArea = GetCurrentArea();

            if (currentArea?.Equals("Admin", StringComparison.OrdinalIgnoreCase) == true)
                return RedirectToAdminLogin(message);
            else
                return RedirectToCustomerLogin(message);
        }

        /// <summary>
        /// Checks if the current user has access to the current area
        /// </summary>
        protected bool HasAreaAccess()
        {
            var currentArea = GetCurrentArea();

            // No area restriction for public areas
            if (string.IsNullOrEmpty(currentArea))
                return true;

            // Admin area requires admin role
            if (currentArea.Equals("Admin", StringComparison.OrdinalIgnoreCase))
                return IsAuthenticated && IsAdmin;

            // Customer area requires customer role OR no authentication (public access)
            if (currentArea.Equals("Customer", StringComparison.OrdinalIgnoreCase))
                return true; // Customer area is public by default

            return true; // Default allow for other areas
        }

        /// <summary>
        /// Checks if user is in wrong area and redirects appropriately
        /// </summary>
        protected IActionResult? CheckAreaAccess()
        {
            var currentArea = GetCurrentArea();

            // Admin area access check
            if (currentArea?.Equals("Admin", StringComparison.OrdinalIgnoreCase) == true)
            {
                if (!IsAuthenticated)
                    return RedirectToAdminLogin("Please login to access admin area.");

                if (!IsAdmin)
                    return RedirectToCustomerLogin("You don't have permission to access admin area.");
            }

            // Customer area access check - redirect admins to admin dashboard
            if (currentArea?.Equals("Customer", StringComparison.OrdinalIgnoreCase) == true)
            {
                if (IsAuthenticated && IsAdmin)
                    return RedirectToAction("Index", "Home", new { area = "Admin" });
            }

            return null; // No redirect needed
        }

        protected Admin? GetCurrentAdmin()
        {
            return GetCurrentAdminAsync().GetAwaiter().GetResult();
        }

        protected User? GetCurrentCustomer()
        {
            return GetCurrentCustomerAsync().GetAwaiter().GetResult();
        }

        protected int? GetCurrentCustomerId()
        {
            var customer = GetCurrentCustomer();
            return customer?.Id;
        }

        protected int? GetCurrentAdminId()
        {
            var admin = GetCurrentAdmin();
            return admin?.Id;
        }

        // Synchronous file upload method for backward compatibility
        protected string SaveUploadedFile(IFormFile file, string folder)
        {
            return SaveUploadedFileAsync(file, folder).GetAwaiter().GetResult();
        }
    }
}
