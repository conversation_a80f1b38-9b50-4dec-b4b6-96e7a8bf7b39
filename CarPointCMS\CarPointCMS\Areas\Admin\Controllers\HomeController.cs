using Microsoft.AspNetCore.Mvc;
using CarPointCMS.Data;
using CarPointCMS.Models.ViewModels;
using CarPointCMS.Models.Entities;
using CarPointCMS.Controllers;
using CarPointCMS.Services;
using CarPointCMS.Attributes;
using CarPointCMS.Common;
using Microsoft.EntityFrameworkCore;

namespace CarPointCMS.Areas.Admin.Controllers
{
    [Area("Admin")]
    [AdminOnly]
    public class HomeController : BaseController
    {
        private readonly ApplicationDbContext _context;

        public HomeController(ApplicationDbContext context, IAuthenticationService authService)
            : base(authService)
        {
            _context = context;
        }

        public async Task<IActionResult> Index()
        {
            // Get dashboard statistics using constants
            var totalActiveCustomers = await _context.Users
                .Where(u => u.Status == UserStatus.Active && u.IsActive)
                .CountAsync();

            var totalPendingCustomers = await _context.Users
                .Where(u => u.Status == UserStatus.Pending || !u.IsActive)
                .CountAsync();

            var totalActiveListings = await _context.Listings
                .Where(l => l.ListingStatus == ListingStatus.Active && l.IsActive)
                .CountAsync();

            var totalPendingListings = await _context.Listings
                .Where(l => l.ListingStatus == ListingStatus.Pending)
                .CountAsync();

            // Create view model
            var viewModel = new AdminDashboardViewModel
            {
                Stats = new AdminStatsViewModel
                {
                    TotalActiveCustomers = totalActiveCustomers,
                    TotalPendingCustomers = totalPendingCustomers,
                    TotalActiveListings = totalActiveListings,
                    TotalPendingListings = totalPendingListings
                }
            };

            // Get recent listings for dashboard
            viewModel.RecentListings = await _context.Listings
                .Include(l => l.ListingBrand)
                .Include(l => l.ListingLocation)
                .Include(l => l.User)
                .OrderByDescending(l => l.CreatedAt)
                .Take(5)
                .ToListAsync();

            // Get recent users
            viewModel.RecentUsers = await _context.Users
                .OrderByDescending(u => u.CreatedAt)
                .Take(5)
                .ToListAsync();

            // Get recent purchases
            viewModel.RecentPurchases = await _context.PackagePurchases
                .Include(pp => pp.Package)
                .Include(pp => pp.User)
                .OrderByDescending(pp => pp.CreatedAt)
                .Take(5)
                .ToListAsync();

            return View(viewModel);
        }
    }
}
