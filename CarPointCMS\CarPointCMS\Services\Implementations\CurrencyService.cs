using System.Globalization;
using CarPointCMS.Models.Entities;
using CarPointCMS.Services.Interfaces;

namespace CarPointCMS.Services.Implementations
{
    public class CurrencyService : ICurrencyService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<CurrencyService> _logger;

        public CurrencyService(IUnitOfWork unitOfWork, ILogger<CurrencyService> logger)
        {
            _unitOfWork = unitOfWork;
            _logger = logger;
        }

        public async Task<IEnumerable<Currency>> GetAllCurrenciesAsync()
        {
            var currencies = await _unitOfWork.Currencies.GetAllAsync();
            return currencies.OrderBy(c => c.Name);
        }

        public async Task<Currency?> GetCurrencyByIdAsync(int id)
        {
            return await _unitOfWork.Currencies.GetByIdAsync(id);
        }

        public async Task<Currency?> GetDefaultCurrencyAsync()
        {
            return await _unitOfWork.Currencies.FirstOrDefaultAsync(c => c.IsDefault == true);
        }

        public async Task<Currency> GetCurrentCurrencyAsync()
        {
            // For now, return the default currency
            // In a real implementation, this could check session/user preferences
            var defaultCurrency = await GetDefaultCurrencyAsync();
            if (defaultCurrency != null)
            {
                return defaultCurrency;
            }

            // If no default currency is set, return USD as fallback
            var usdCurrency = await _unitOfWork.Currencies.FirstOrDefaultAsync(c => c.Name == "USD");
            if (usdCurrency != null)
            {
                return usdCurrency;
            }

            // If USD doesn't exist, return the first available currency
            var firstCurrency = await _unitOfWork.Currencies.FirstOrDefaultAsync();
            if (firstCurrency != null)
            {
                return firstCurrency;
            }

            // If no currencies exist, create a default USD currency
            var defaultUsd = new Currency
            {
                Name = "USD",
                Symbol = "$",
                Value = 1.0m,
                IsDefault = true,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            await _unitOfWork.Currencies.AddAsync(defaultUsd);
            await _unitOfWork.SaveChangesAsync();

            return defaultUsd;
        }

        public async Task<Currency> CreateCurrencyAsync(Currency currency)
        {
            // If this is set as default, update other currencies
            if (currency.IsDefault == true)
            {
                await SetAllCurrenciesAsNonDefaultAsync();
            }

            currency.CreatedAt = DateTime.UtcNow;
            currency.UpdatedAt = DateTime.UtcNow;

            await _unitOfWork.Currencies.AddAsync(currency);
            await _unitOfWork.SaveChangesAsync();

            return currency;
        }

        public async Task<Currency> UpdateCurrencyAsync(Currency currency)
        {
            // If this is set as default, update other currencies
            if (currency.IsDefault == true)
            {
                await SetAllCurrenciesAsNonDefaultAsync();
            }

            currency.UpdatedAt = DateTime.UtcNow;

            await _unitOfWork.Currencies.UpdateAsync(currency);
            await _unitOfWork.SaveChangesAsync();

            return currency;
        }

        public async Task<bool> DeleteCurrencyAsync(int id)
        {
            var currency = await _unitOfWork.Currencies.GetByIdAsync(id);
            if (currency == null)
                return false;

            // Cannot delete default currency
            if (currency.IsDefault == true)
                return false;

            await _unitOfWork.Currencies.DeleteAsync(currency);
            await _unitOfWork.SaveChangesAsync();

            return true;
        }

        public async Task<bool> SetDefaultCurrencyAsync(int id)
        {
            var currency = await _unitOfWork.Currencies.GetByIdAsync(id);
            if (currency == null)
                return false;

            // Set all currencies as non-default
            await SetAllCurrenciesAsNonDefaultAsync();

            // Set the specified currency as default
            currency.IsDefault = true;
            currency.UpdatedAt = DateTime.UtcNow;

            await _unitOfWork.Currencies.UpdateAsync(currency);
            await _unitOfWork.SaveChangesAsync();

            return true;
        }

        public async Task<bool> ExistsAsync(int id)
        {
            return await _unitOfWork.Currencies.AnyAsync(c => c.Id == id);
        }

        public async Task<decimal> ConvertAmountAsync(decimal amount, int fromCurrencyId, int toCurrencyId)
        {
            if (fromCurrencyId == toCurrencyId)
                return amount;

            var fromCurrency = await _unitOfWork.Currencies.GetByIdAsync(fromCurrencyId);
            var toCurrency = await _unitOfWork.Currencies.GetByIdAsync(toCurrencyId);

            if (fromCurrency == null || toCurrency == null)
                throw new ArgumentException("Invalid currency ID");

            // Convert to base currency (USD) first, then to target currency
            var baseAmount = amount / fromCurrency.Value;
            var convertedAmount = baseAmount * toCurrency.Value;

            return Math.Round(convertedAmount, 2);
        }

        public async Task<decimal> ConvertToDefaultAsync(decimal amount, int fromCurrencyId)
        {
            var defaultCurrency = await GetDefaultCurrencyAsync();
            if (defaultCurrency == null)
                throw new InvalidOperationException("No default currency set");

            return await ConvertAmountAsync(amount, fromCurrencyId, defaultCurrency.Id);
        }

        public async Task<decimal> ConvertFromDefaultAsync(decimal amount, int toCurrencyId)
        {
            var defaultCurrency = await GetDefaultCurrencyAsync();
            if (defaultCurrency == null)
                throw new InvalidOperationException("No default currency set");

            return await ConvertAmountAsync(amount, defaultCurrency.Id, toCurrencyId);
        }

        public async Task<string> FormatAmountAsync(decimal amount, int currencyId)
        {
            var currency = await _unitOfWork.Currencies.GetByIdAsync(currencyId);
            if (currency == null)
                throw new ArgumentException("Invalid currency ID");

            return FormatCurrency(amount, currency.Symbol);
        }

        public async Task<string> FormatAmountWithDefaultCurrencyAsync(decimal amount)
        {
            var defaultCurrency = await GetDefaultCurrencyAsync();
            if (defaultCurrency == null)
                throw new InvalidOperationException("No default currency set");

            return FormatCurrency(amount, defaultCurrency.Symbol);
        }

        public async Task<bool> UpdateExchangeRateAsync(int currencyId, decimal newRate)
        {
            var currency = await _unitOfWork.Currencies.GetByIdAsync(currencyId);
            if (currency == null)
                return false;

            currency.Value = newRate;
            currency.UpdatedAt = DateTime.UtcNow;

            await _unitOfWork.Currencies.UpdateAsync(currency);
            await _unitOfWork.SaveChangesAsync();

            _logger.LogInformation($"Exchange rate updated for {currency.Name}: {newRate}");
            return true;
        }

        public async Task<bool> UpdateAllExchangeRatesAsync(Dictionary<int, decimal> rates)
        {
            try
            {
                foreach (var rate in rates)
                {
                    await UpdateExchangeRateAsync(rate.Key, rate.Value);
                }
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to update exchange rates");
                return false;
            }
        }

        public async Task<DateTime?> GetLastUpdateTimeAsync()
        {
            var currencies = await _unitOfWork.Currencies.GetAllAsync();
            return currencies.Max(c => c.UpdatedAt);
        }

        public string GetCurrencySymbol(Currency currency)
        {
            return currency.Symbol;
        }

        public string FormatCurrency(decimal amount, string symbol)
        {
            return $"{symbol}{amount:N2}";
        }

        public async Task<List<Currency>> GetActiveCurrenciesAsync()
        {
            var currencies = await _unitOfWork.Currencies.GetAllAsync();
            return currencies.ToList(); // All currencies are considered active for now
        }

        private async Task SetAllCurrenciesAsNonDefaultAsync()
        {
            var currencies = await _unitOfWork.Currencies.FindAsync(c => c.IsDefault);
            foreach (var currency in currencies)
            {
                currency.IsDefault = false;
                currency.UpdatedAt = DateTime.UtcNow;
                await _unitOfWork.Currencies.UpdateAsync(currency);
            }
        }
    }
}
