using CarPointCMS.Data;
using CarPointCMS.Models;
using CarPointCMS.Models.Entities;
using CarPointCMS.Models.ViewModels;
using CarPointCMS.Common;
using CarPointCMS.Controllers;
using CarPointCMS.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.Diagnostics;

namespace CarPointCMS.Areas.Customer.Controllers
{
    [Area("Customer")]
    public class HomeController : BaseController
    {
        private readonly ILogger<HomeController> _logger;
        private readonly ApplicationDbContext _db;

        public HomeController(ILogger<HomeController> logger, ApplicationDbContext db, IAuthenticationService authService)
            : base(authService)
        {
            _logger = logger;
            _db = db;
        }

        public async Task<IActionResult> Index()
        {
            // Check area access - redirect admins to admin dashboard
            var areaCheck = CheckAreaAccess();
            if (areaCheck != null)
                return areaCheck;

            try
            {
                var model = new HomeViewModel
                {
                    PageHome = await _db.PageHomeItems.FirstOrDefaultAsync() ?? new PageHomeItem(),
                    HomeAdvertisement = await _db.HomeAdvertisements.FirstOrDefaultAsync() ?? new HomeAdvertisement(),
                    Testimonials = await _db.Testimonials.ToListAsync(),

                    // Get brands with listing counts
                    ListingBrands = await _db.ListingBrands
                        .Select(b => new ListingBrandWithCount
                        {
                            Brand = b,
                            ListingCount = _db.Listings.Count(l => l.ListingBrandId == b.Id && l.ListingStatus == ListingStatus.Active)
                        })
                        .OrderByDescending(b => b.ListingCount)
                        .ToListAsync(),

                    // Get locations with listing counts
                    ListingLocations = await _db.ListingLocations
                        .Select(l => new ListingLocationWithCount
                        {
                            Location = l,
                            ListingCount = _db.Listings.Count(listing => listing.ListingLocationId == l.Id && listing.ListingStatus == ListingStatus.Active)
                        })
                        .OrderByDescending(l => l.ListingCount)
                        .ToListAsync(),

                    // Get featured listings
                    FeaturedListings = await _db.Listings
                        .Include(l => l.ListingBrand)
                        .Include(l => l.ListingLocation)
                        .Include(l => l.User)
                        .Where(l => l.ListingStatus == ListingStatus.Active && l.IsFeatured == true)
                        .OrderBy(l => l.ListingName)
                        .ToListAsync()
                };

                return View(model);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading home page");
                return View(new HomeViewModel());
            }
        }

        public async Task<IActionResult> About()
        {
            var model = await _db.PageAboutItems.FirstOrDefaultAsync();
            return View(model);
        }

        public async Task<IActionResult> Contact()
        {
            var model = await _db.PageContactItems.FirstOrDefaultAsync();
            return View(model);
        }

        [HttpPost]
        public async Task<IActionResult> Contact(ContactFormViewModel model)
        {
            if (!ModelState.IsValid)
            {
                var pageModel = await _db.PageContactItems.FirstOrDefaultAsync();
                ViewBag.PageContact = pageModel;
                return View(model);
            }

            try
            {
                // Here you would implement email sending logic
                // For now, just return success
                TempData["Success"] = "Your message has been sent successfully!";
                return RedirectToAction("Contact");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending contact form");
                ModelState.AddModelError("", "An error occurred while sending your message. Please try again.");
                var pageModel = await _db.PageContactItems.FirstOrDefaultAsync();
                ViewBag.PageContact = pageModel;
                return View(model);
            }
        }

        public async Task<IActionResult> Faq()
        {
            var model = new FaqViewModel
            {
                PageFaqItem = await _db.PageFaqItems.FirstOrDefaultAsync() ?? new PageFaqItem(),
                Faqs = await _db.Faqs.ToListAsync()
            };
            return View(model);
        }

        public async Task<IActionResult> Pricing()
        {
            var model = new PricingViewModel
            {
                PagePricingItem = await _db.PagePricingItems.FirstOrDefaultAsync() ?? new PagePricingItem(),
                Packages = await _db.Packages.ToListAsync()
            };
            return View(model);
        }

        public async Task<IActionResult> Blog(int? pageIndex)
        {
            var query = _db.Blogs.AsNoTracking().AsQueryable();

            // Number of blog posts per page
            int pageSize = 6;
            var model = new BlogViewModel
            {
                Blogs = await PaginatedList<Blog>.CreateAsync(query, pageIndex ?? 1, pageSize),
                PageBlog = await _db.PageBlogItems.FirstOrDefaultAsync() ?? new PageBlogItem(),
            };

            return View(model);
        }

        public IActionResult Privacy()
        {
            return View();
        }

        [ResponseCache(Duration = 0, Location = ResponseCacheLocation.None, NoStore = true)]
        public IActionResult Error()
        {
            return View(new ErrorViewModel { RequestId = Activity.Current?.Id ?? HttpContext.TraceIdentifier });
        }
    }
}
